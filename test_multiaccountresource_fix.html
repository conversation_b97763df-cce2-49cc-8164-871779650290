<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multi-Account Resource Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .hierarchy-header { padding: 8px; border: 1px solid #ccc; margin: 4px 0; cursor: pointer; }
        .hierarchy-header:hover { background-color: #f5f5f5; }
        .expand-icon { display: inline-block; transition: transform 0.2s; }
        .expand-icon.expanded { transform: rotate(90deg); }
        .hidden { display: none; }
        .ml-3 { margin-left: 12px; }
        .ml-4 { margin-left: 16px; }
        .mb-3 { margin-bottom: 12px; }
        .mb-4 { margin-bottom: 16px; }
    </style>
</head>
<body>
    <h1>Test Multi-Account Resource Count Fix</h1>
    <div id="resourceHierarchy"></div>

    <script>
        // Test data structure
        window.multiAccountResourceData = [
            {
                account_id: "test_account_1",
                account_name: "Test Account 1",
                resource_types: [
                    {
                        type: "ECS::Instance",
                        resources: [
                            { name: "instance-1", type: "ECS::Instance", info: "{}" },
                            { name: "instance-2", type: "ECS::Instance", info: "{}" },
                            { name: "instance-3", type: "ECS::Instance", info: "{}" }
                        ]
                    },
                    {
                        type: "RDS::Database",
                        resources: [
                            { name: "db-1", type: "RDS::Database", info: "{}" },
                            { name: "db-2", type: "RDS::Database", info: "{}" }
                        ]
                    }
                ]
            },
            {
                account_id: "test_account_2", 
                account_name: "Test Account 2",
                resource_types: [
                    {
                        type: "OSS::Bucket",
                        resources: [
                            { name: "bucket-1", type: "OSS::Bucket", info: "{}" }
                        ]
                    }
                ]
            }
        ];

        window.multiAccountResourceStats = {
            total_resources: 6,
            total_accounts: 2,
            total_resource_types: 3
        };
    </script>
    <script src="html/multiaccountresource/multiaccountresource_report.js"></script>

    <div style="margin-top: 20px; padding: 10px; background-color: #f0f8ff; border: 1px solid #0066cc;">
        <h3>Expected Results:</h3>
        <ul>
            <li><strong>Test Account 1</strong> should show <strong>(5)</strong> - total of 3 ECS instances + 2 RDS databases</li>
            <li><strong>Test Account 2</strong> should show <strong>(1)</strong> - total of 1 OSS bucket</li>
            <li><strong>ECS::Instance</strong> should show <strong>(3)</strong> - 3 individual instances</li>
            <li><strong>RDS::Database</strong> should show <strong>(2)</strong> - 2 individual databases</li>
            <li><strong>OSS::Bucket</strong> should show <strong>(1)</strong> - 1 individual bucket</li>
        </ul>
    </div>
</body>
</html>
